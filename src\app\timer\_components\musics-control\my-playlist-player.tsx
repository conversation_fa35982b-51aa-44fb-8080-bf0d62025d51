'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Music,
  Play,
  Plus,
  ExternalLink,
  Heart,
  Clock,
  ChevronRight,
  Leaf
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { motion, AnimatePresence } from 'framer-motion';
import { useUserStore } from '@/store/userStore';
import { useGetMusicPlaylistsUser } from '@schemas/MusicPlaylist/music-playlist-user-query';
import { MainPlayer } from './main-player';
import { useMainPlayer } from './use-main-player';
import { usePlaylistNatureSounds } from './use-playlist-nature-sounds';
import { PlaylistNatureSounds } from './playlist-nature-sounds';
import Link from 'next/link';
import Image from 'next/image';

interface MyPlaylistPlayerProps {
  className?: string;
}

export function MyPlaylistPlayer({ className }: MyPlaylistPlayerProps) {
  const { isAuthenticated } = useUserStore();
  const [selectedPlaylist, setSelectedPlaylist] = useState<any>(null);

  // Fetch user playlists only if authenticated
  const { data: playlists, isLoading } = useGetMusicPlaylistsUser({
    isPublic: false
  });

  // Format playlist for main player hook
  const formattedPlaylist = selectedPlaylist ? {
    id: selectedPlaylist.id,
    name: selectedPlaylist.name,
    musics: selectedPlaylist.musics || [],
    natureSounds: selectedPlaylist.natureSounds || []
  } : null;

  // Main player hook for selected playlist
  const mainPlayerHook = useMainPlayer(formattedPlaylist);

  // Natural sounds hook for selected playlist
  const natureSoundsHook = usePlaylistNatureSounds({
    natureSounds: selectedPlaylist?.natureSounds || []
  });

  // Create main player props
  const mainPlayerProps = {
    currentTrackTitle: formattedPlaylist?.musics?.[mainPlayerHook.currentMusicIndex]?.title || 'No track selected',
    playlistName: formattedPlaylist?.name || 'Playlist',
    currentIndex: mainPlayerHook.currentMusicIndex,
    tracksTotal: formattedPlaylist?.musics?.length || 0,
    isPlaying: mainPlayerHook.isMainPlaying,
    volume: mainPlayerHook.mainVolume,
    isMuted: mainPlayerHook.isMainMuted,
    playbackMode: mainPlayerHook.playbackMode,
    onTogglePlay: mainPlayerHook.toggleMainPlay,
    onSkipNext: mainPlayerHook.skipToNext,
    onSkipPrevious: mainPlayerHook.skipToPrevious,
    onToggleMute: mainPlayerHook.toggleMainMute,
    onTogglePlaybackMode: mainPlayerHook.togglePlaybackMode,
    onVolumeChange: mainPlayerHook.handleMainVolumeChange,
    onSeek: mainPlayerHook.handleSeek,
    currentTime: mainPlayerHook.currentTime,
    duration: mainPlayerHook.duration,
    audioRef: mainPlayerHook.mainAudioRef,
    disabled: !formattedPlaylist || (formattedPlaylist.musics?.length || 0) === 0
  };

  // Reset selected playlist when user logs out
  useEffect(() => {
    if (!isAuthenticated) {
      setSelectedPlaylist(null);
    }
  }, [isAuthenticated]);



  // Handle playlist selection
  const handlePlaylistSelect = (playlist: any) => {
    setSelectedPlaylist(playlist);
  };

  // Handle back to playlist list
  const handleBackToList = () => {
    setSelectedPlaylist(null);
  };

  // If user is not authenticated
  if (!isAuthenticated) {
    return (
      <div className="flex flex-col items-center justify-center py-8 px-4 text-center space-y-3">
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.3, ease: "easeOut" }}
          className="relative"
        >
          <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-primary/10 rounded-full blur-xl scale-150" />
          <div className="relative w-12 h-12 rounded-full bg-gradient-to-br from-primary/20 to-primary/10 flex items-center justify-center border border-primary/20">
            <Music className="h-6 w-6 text-primary" />
          </div>
        </motion.div>
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1, duration: 0.3 }}
          className="space-y-1"
        >
          <h3 className="text-base font-semibold text-foreground">My Playlist</h3>
          <p className="text-xs text-muted-foreground max-w-[240px] leading-relaxed">
            Sign in to access your personal music playlists and create custom collections for your focus sessions.
          </p>
        </motion.div>
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.2, duration: 0.3 }}
        >
          <Button
            variant="default"
            size="sm"
            asChild
            className="bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 shadow-lg hover:shadow-xl transition-all duration-200"
          >
            <Link href="/auth/signin">
              <Heart className="h-3.5 w-3.5 mr-1.5" />
              Sign In
            </Link>
          </Button>
        </motion.div>
      </div>
    );
  }

  // If selected playlist, show main player
  if (selectedPlaylist) {
    const totalTracks = (selectedPlaylist.musics?.length || 0) + (selectedPlaylist.videos?.length || 0) + (selectedPlaylist.natureSounds?.length || 0);
    const musicTracks = selectedPlaylist.musics?.length || 0;
    const natureSoundTracks = selectedPlaylist.natureSounds?.length || 0;

    return (
      <div className={cn("space-y-3", className)}>
        {/* Header with back button */}
        <div className="flex items-center gap-2 pb-1.5 border-b border-border/30">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleBackToList}
            className="h-7 px-1.5 text-muted-foreground hover:text-foreground"
          >
            <ChevronRight className="h-3.5 w-3.5 rotate-180" />
            <span className="text-xs">Back</span>
          </Button>
          <div className="flex-1 min-w-0">
            <h3 className="font-medium text-xs truncate">{selectedPlaylist.name}</h3>
            <p className="text-xs text-muted-foreground">
              {totalTracks} tracks • {musicTracks} audio{natureSoundTracks > 0 ? ` • ${natureSoundTracks} nature` : ''}
            </p>
          </div>
        </div>

        {/* Main Player or Empty State */}
        {musicTracks > 0 ? (
          <div className="space-y-3">
            <MainPlayer {...mainPlayerProps} />
            {/* Natural Sounds Section */}
            {natureSoundTracks > 0 && (
              <PlaylistNatureSounds
                players={natureSoundsHook.players}
                playingCount={natureSoundsHook.playingCount}
                onToggleSound={natureSoundsHook.toggleSound}
                onVolumeChange={natureSoundsHook.handleVolumeChange}
                onToggleMute={natureSoundsHook.toggleMute}
              />
            )}
          </div>
        ) : natureSoundTracks > 0 ? (
          <div className="space-y-3">
            {/* Only Natural Sounds - No Music */}
            <div className="flex flex-col items-center justify-center py-4 px-3 text-center space-y-2">
              <div className="w-8 h-8 rounded-full bg-gradient-to-br from-emerald-500/20 to-emerald-500/10 flex items-center justify-center">
                <Leaf className="h-4 w-4 text-emerald-500" />
              </div>
              <div className="space-y-1">
                <h4 className="font-medium text-xs">Nature Sounds Only</h4>
                <p className="text-xs text-muted-foreground max-w-[180px] leading-relaxed">
                  This playlist contains nature sounds for ambient focus.
                </p>
              </div>
            </div>
            <PlaylistNatureSounds
              players={natureSoundsHook.players}
              playingCount={natureSoundsHook.playingCount}
              onToggleSound={natureSoundsHook.toggleSound}
              onVolumeChange={natureSoundsHook.handleVolumeChange}
              onToggleMute={natureSoundsHook.toggleMute}
            />
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-6 px-3 text-center space-y-2">
            <div className="w-10 h-10 rounded-full bg-gradient-to-br from-muted/50 to-muted/30 flex items-center justify-center">
              <Music className="h-5 w-5 text-muted-foreground" />
            </div>
            <div className="space-y-1">
              <h4 className="font-medium text-xs">No Audio Content</h4>
              <p className="text-xs text-muted-foreground max-w-[180px] leading-relaxed">
                This playlist doesn't contain any audio tracks or nature sounds.
              </p>
            </div>
            <Button variant="outline" size="sm" asChild>
              <Link href={`/dashboard/playlist/${selectedPlaylist.id}`}>
                <Plus className="h-3 w-3 mr-1" />
                Add Content
              </Link>
            </Button>
          </div>
        )}
      </div>
    );
  }

  // Loading state
  if (isLoading) {
    return (
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Skeleton className="h-6 w-6 rounded-lg" />
            <div className="space-y-1">
              <Skeleton className="h-3 w-20" />
              <Skeleton className="h-2.5 w-14" />
            </div>
          </div>
          <Skeleton className="h-5 w-12 rounded-md" />
        </div>
        <div className="space-y-1.5">
          {Array.from({ length: 3 }).map((_, i) => (
            <motion.div
              key={i}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: i * 0.1, duration: 0.3 }}
              className="flex items-center gap-2.5 px-2 py-2 rounded-lg border border-muted/50"
            >
              <Skeleton className="h-10 w-10 rounded-lg" />
              <div className="flex-1 space-y-1.5">
                <Skeleton className="h-3 w-28" />
                <div className="flex gap-1.5">
                  <Skeleton className="h-2.5 w-10" />
                  <Skeleton className="h-2.5 w-10" />
                  <Skeleton className="h-2.5 w-12" />
                </div>
              </div>
              <Skeleton className="h-7 w-7 rounded-full" />
            </motion.div>
          ))}
        </div>
      </div>
    );
  }

  // No playlists available
  if (!playlists || playlists.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-8 px-4 text-center space-y-3">
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.3, ease: "easeOut" }}
          className="relative"
        >
          <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-primary/10 rounded-full blur-xl scale-150" />
          <div className="relative w-12 h-12 rounded-full bg-gradient-to-br from-primary/20 to-primary/10 flex items-center justify-center border border-primary/20">
            <Music className="h-6 w-6 text-primary" />
          </div>
        </motion.div>
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1, duration: 0.3 }}
          className="space-y-1"
        >
          <h3 className="text-base font-semibold text-foreground">No Playlists Yet</h3>
          <p className="text-xs text-muted-foreground max-w-[240px] leading-relaxed">
            Create your first playlist to start building your personal music collection for focused work sessions.
          </p>
        </motion.div>
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2, duration: 0.3 }}
          className="flex flex-col gap-1.5"
        >
          <Button
            variant="default"
            size="sm"
            asChild
            className="bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 shadow-lg hover:shadow-xl transition-all duration-200"
          >
            <Link href="/dashboard/playlist">
              <Plus className="h-3.5 w-3.5 mr-1.5" />
              Create Playlist
            </Link>
          </Button>
          <Button variant="ghost" size="sm" asChild className="text-muted-foreground hover:text-foreground">
            <Link href="/dashboard/playlist">
              <ExternalLink className="h-3 w-3 mr-1.5" />
              Manage Playlists
            </Link>
          </Button>
        </motion.div>
      </div>
    );
  }

  // Render playlist list
  return (
    <div className={cn("space-y-3", className)}>
      {/* Enhanced Header */}
      <div className="flex items-center gap-1.5">
        <div className="p-1 rounded-lg bg-gradient-to-br from-primary/20 to-primary/10">
          <Heart className="h-3 w-3 text-primary" />
        </div>
        <div>
          <h3 className="font-medium text-xs">My Playlists</h3>
          <p className="text-xs text-muted-foreground">
            {playlists.length} collection{playlists.length !== 1 ? 's' : ''}
          </p>
        </div>
      </div>

      {/* Compact Playlist List */}
      <div className="space-y-1.5">
        <AnimatePresence mode="popLayout">
          {playlists.map((playlist, index) => (
            <motion.div
              key={playlist.id}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{
                duration: 0.2,
                delay: index * 0.05,
                ease: "easeOut"
              }}
            >
              <PlaylistItem
                playlist={playlist}
                onSelect={handlePlaylistSelect}
              />
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {/* Enhanced Manage Link */}
      <div className="pt-2 border-t border-border/30">
        <Button
          variant="ghost"
          size="sm"
          asChild
          className="w-full justify-start text-muted-foreground hover:text-foreground hover:bg-muted/50 transition-colors"
        >
          <Link href="/dashboard/playlist">
            <ExternalLink className="h-3 w-3 mr-1.5" />
            <span className="text-xs">Manage Playlists</span>
          </Link>
        </Button>
      </div>
    </div>
  );
}

// Playlist Item Component
interface PlaylistItemProps {
  playlist: any;
  onSelect: (playlist: any) => void;
}

function PlaylistItem({ playlist, onSelect }: PlaylistItemProps) {
  const totalTracks = (playlist.musics?.length || 0) + (playlist.videos?.length || 0) + (playlist.natureSounds?.length || 0);
  const musicTracks = playlist.musics?.length || 0;
  const natureSoundTracks = playlist.natureSounds?.length || 0;
  const estimatedDuration = Math.floor(totalTracks * 3.5);
  const [isHovered, setIsHovered] = useState(false);

  return (
    <Card
      className={cn(
        "group cursor-pointer transition-all duration-200 border-muted/50 overflow-hidden relative",
        "hover:shadow-md hover:shadow-primary/5 hover:border-primary/20",
        "active:scale-[0.98] transform-gpu"
      )}
      onClick={() => onSelect(playlist)}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Background Image */}
      {playlist.imageUrl && (
        <div className="absolute inset-0">
          <Image
            src={playlist.imageUrl}
            alt={playlist.name}
            fill
            className="object-cover group-hover:scale-105 transition-transform duration-300"
            sizes="370px"
          />
          {/* Semi-transparent overlay for text readability */}
          <div className="absolute inset-0 bg-background/75 group-hover:bg-background/70 transition-colors duration-200" />
        </div>
      )}

      <CardContent className="px-2 sm:px-3 py-2 relative z-10">
        <div className="flex items-center gap-2.5">
          {/* Enhanced Thumbnail - Only show when no background image */}
          {!playlist.imageUrl && (
            <div className="relative w-10 h-10 rounded-lg overflow-hidden shrink-0 bg-gradient-to-br from-primary/10 to-primary/5">
              <div className="w-full h-full bg-gradient-to-br from-primary/20 to-primary/10 flex items-center justify-center relative">
                <div className="absolute inset-0 bg-gradient-to-r from-primary/10 to-transparent opacity-50" />
                <Music className="h-4 w-4 text-primary/70 relative z-10" />
              </div>
            </div>
          )}

          {/* Enhanced Content - Compact Layout */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between gap-2">
              <h4 className={cn(
                "font-medium text-xs line-clamp-1 transition-colors duration-200",
                playlist.imageUrl
                  ? "text-foreground group-hover:text-primary"
                  : "text-foreground group-hover:text-primary"
              )}>
                {playlist.name}
              </h4>

              {/* Duration and Play Button Container */}
              <div className="flex flex-col items-end gap-1 shrink-0">
                {estimatedDuration > 0 && (
                  <div className={cn(
                    "flex items-center gap-0.5 text-xs",
                    playlist.imageUrl ? "text-foreground/80" : "text-muted-foreground"
                  )}>
                    <Clock className="h-2 w-2" />
                    <span>{estimatedDuration}m</span>
                  </div>
                )}

                {/* Repositioned Play Button */}
                <div className={cn(
                  "transition-all duration-200",
                  isHovered ? "opacity-100 scale-100" : "opacity-0 scale-95"
                )}>
                  <Button
                    variant="ghost"
                    size="sm"
                    className={cn(
                      "h-6 w-6 p-0 rounded-full",
                      playlist.imageUrl
                        ? "bg-background/80 hover:bg-background/90 text-foreground border border-border/50"
                        : "bg-primary/10 hover:bg-primary/20 text-primary border border-primary/20",
                      "shadow-sm backdrop-blur-sm"
                    )}
                  >
                    <Play className="h-2.5 w-2.5 ml-0.5" />
                  </Button>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-1.5 mt-0.5 flex-wrap">
              {musicTracks > 0 && (
                <div className={cn(
                  "flex items-center gap-0.5 text-xs",
                  playlist.imageUrl ? "text-foreground/70" : "text-muted-foreground"
                )}>
                  <div className="w-1 h-1 rounded-full bg-green-500" />
                  <span>{musicTracks} audio</span>
                </div>
              )}
              {playlist.videos?.length > 0 && (
                <div className={cn(
                  "flex items-center gap-0.5 text-xs",
                  playlist.imageUrl ? "text-foreground/70" : "text-muted-foreground"
                )}>
                  <div className="w-1 h-1 rounded-full bg-red-500" />
                  <span>{playlist.videos.length} video</span>
                </div>
              )}
              {natureSoundTracks > 0 && (
                <div className={cn(
                  "flex items-center gap-0.5 text-xs",
                  playlist.imageUrl ? "text-foreground/70" : "text-muted-foreground"
                )}>
                  <div className="w-1 h-1 rounded-full bg-emerald-500" />
                  <span>{natureSoundTracks} nature</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
